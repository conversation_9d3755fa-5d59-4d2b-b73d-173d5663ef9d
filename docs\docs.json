{"$schema": "https://mintlify.com/docs.json", "theme": "mint", "name": "<PERSON><PERSON>'s Doc", "colors": {"primary": "#3B82F6", "light": "#60A5FA", "dark": "#1D4ED8"}, "favicon": "/favicon.svg", "navigation": {"tabs": [{"tab": "Product Guides", "groups": [{"group": "Get Started", "pages": ["index", "quickstart", "development"]}, {"group": "AI Development Tools", "pages": ["products/task-master-ai", "products/codecraft-cli"]}, {"group": "Essentials", "pages": ["essentials/markdown", "essentials/code", "essentials/images", "essentials/settings", "essentials/navigation", "essentials/reusable-snippets"]}]}, {"tab": "API Reference", "groups": [{"group": "API Documentation", "pages": ["api-reference/introduction"]}, {"group": "Endpoint Examples", "pages": ["api-reference/endpoint/get", "api-reference/endpoint/create", "api-reference/endpoint/delete", "api-reference/endpoint/webhook"]}]}], "global": {"anchors": [{"anchor": "Portfolio", "href": "https://im-rahul.netlify.app/#", "icon": "user"}, {"anchor": "GitHub", "href": "https://github.com/im-rahulr", "icon": "github"}, {"anchor": "Contact", "href": "https://im-rahul.netlify.app/#contact", "icon": "envelope"}]}}, "logo": {"light": "/logo/light.svg", "dark": "/logo/dark.svg"}, "navbar": {"links": [{"label": "Contact", "href": "https://im-rahul.netlify.app/#contact"}], "primary": {"type": "button", "label": "Portfolio", "href": "https://im-rahul.netlify.app/#"}}, "footer": {"socials": {"github": "https://github.com/im-rahulr", "linkedin": "https://linkedin.com/in/rahul-r-developer", "website": "https://im-rahul.netlify.app/#"}}}