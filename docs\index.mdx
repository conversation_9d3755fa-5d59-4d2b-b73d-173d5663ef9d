---
title: "Welcome to <PERSON><PERSON>'s Doc"
description: "Your comprehensive guide to AI-powered development tools and innovative solutions"
---

<img
  className="block dark:hidden"
  src="/images/hero-light.png"
  alt="Hero Light"
/>
<img
  className="hidden dark:block"
  src="/images/hero-dark.png"
  alt="Hero Dark"
/>

## 🚀 AI Development Tools

Discover powerful AI-driven tools designed to accelerate your development workflow and enhance productivity.

<CardGroup cols={2}>
  <Card
    title="Task Master AI"
    icon="tasks"
    href="/products/task-master-ai"
  >
    A comprehensive task management system for AI-driven development with <PERSON>, designed for seamless integration with Cursor AI
  </Card>
  <Card
    title="CodeCraft CLI"
    icon="terminal"
    href="/products/codecraft-cli"
  >
    A powerful command-line AI workflow tool powered by Google's Gemini AI technology for intelligent coding assistance
  </Card>
</CardGroup>

## 🛠️ Getting Started

Everything you need to know to get up and running with <PERSON><PERSON>'s development tools.

<CardGroup cols={2}>
  <Card
    title="Quick Start Guide"
    icon="rocket"
    href="/quickstart"
  >
    Get started quickly with step-by-step installation and setup instructions
  </Card>
  <Card
    title="Development Setup"
    icon="code"
    href="/development"
  >
    Set up your development environment for optimal productivity
  </Card>
</CardGroup>

## 🎯 About the Developer

<Card
  title="Rahul's Portfolio"
  icon="user"
  href="https://im-rahul.netlify.app/#"
>
  Learn more about Rahul, a passionate software developer focused on creating innovative AI-powered development tools. Visit the portfolio to see more projects and get in touch.
</Card>

## 📚 Documentation Features

Explore the comprehensive documentation system built for developers.

<CardGroup cols={2}>
  <Card
    title="Markdown Support"
    icon="markdown"
    href="/essentials/markdown"
  >
    Rich markdown support with syntax highlighting and interactive components
  </Card>
  <Card
    title="Code Examples"
    icon="code"
    href="/essentials/code"
  >
    Interactive code examples and snippets for better understanding
  </Card>
  <Card
    title="Navigation"
    icon="compass"
    href="/essentials/navigation"
  >
    Intuitive navigation system for easy content discovery
  </Card>
  <Card
    title="Settings"
    icon="gear"
    href="/essentials/settings"
  >
    Customizable settings and configuration options
  </Card>
</CardGroup>
